"""
Cloudbeds API client module.

This module provides functions to interact with the Cloudbeds API.
"""

import asyncio
import time
import logging
import datetime
from typing import Dict, List, Optional, Any, Union

import httpx

from src.config import CLOUDBEDS_API_KEY, CLOUDBEDS_PROPERTY_ID, API_BASE_URL, API_RATE_LIMIT

# Set up logging
logger = logging.getLogger("cloudbeds_client")

def get_access_token() -> str:
    """
    Get access token from Cloudbeds API using API key.

    Returns:
        str: Access token
    """
    # For the Cloudbeds API, we can use the API key directly as the access token
    return CLOUDBEDS_API_KEY

async def api_request(
    endpoint: str,
    params: Optional[Dict[str, Any]] = None,
    property_id: Optional[str] = None,
    method: str = "GET",
    data: Optional[Dict[str, Any]] = None
) -> Union[List[Dict[str, Any]], Dict[str, Any]]:
    """
    Make an async request to the Cloudbeds API with rate limiting.

    Args:
        endpoint (str): API endpoint
        params (dict, optional): Request parameters for GET requests
        property_id (str, optional): Cloudbeds property ID
        method (str): HTTP method (GET, POST, PUT, PATCH, DELETE)
        data (dict, optional): Request body data for POST/PUT requests

    Returns:
        Union[List[Dict[str, Any]], Dict[str, Any]]: API response data
    """
    url = f"{API_BASE_URL}/{endpoint}"

    # Initialize parameters if not provided
    if params is None:
        params = {}

    # Add property ID to URL params for all requests (this is the standard way)
    if property_id:
        params["propertyID"] = property_id
    elif CLOUDBEDS_PROPERTY_ID:
        params["propertyID"] = CLOUDBEDS_PROPERTY_ID

    # Request headers
    headers = {
        "Authorization": f"Bearer {get_access_token()}",
        "Content-Type": "application/json"
    }

    try:
        # Make API request
        async with httpx.AsyncClient() as client:
            if method.upper() == "GET":
                response = await client.get(url, params=params, headers=headers)
            elif method.upper() == "POST":
                # For POST requests, property ID is in URL params, not in body
                if data is None:
                    data = {}
                print(f"DEBUG POST: URL={url}, params={params}, data={data}")
                
                # Special handling for postReservation endpoint which expects form data
                if endpoint == "postReservation":
                    # Use application/x-www-form-urlencoded instead of multipart/form-data
                    headers_form = {k: v for k, v in headers.items() if k != "Content-Type"}
                    headers_form["Content-Type"] = "application/x-www-form-urlencoded"
                    response = await client.post(url, data=data, headers=headers_form, params=params)
                else:
                    response = await client.post(url, json=data, headers=headers, params=params)
            elif method.upper() == "PUT":
                # For PUT requests, property ID is in URL params, not in body
                if data is None:
                    data = {}
                response = await client.put(url, json=data, headers=headers, params=params)
            elif method.upper() == "PATCH":
                # For PATCH requests, property ID is in URL params, not in body
                if data is None:
                    data = {}
                response = await client.patch(url, json=data, headers=headers, params=params)
            elif method.upper() == "DELETE":
                response = await client.delete(url, params=params, headers=headers)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")

        # Respect rate limit
        await asyncio.sleep(1.0 / API_RATE_LIMIT)

        # Log response for debugging
        print(f"DEBUG RESPONSE: Status={response.status_code}, Text={response.text[:200]}...")

        # Handle response
        if response.status_code in [200, 201, 202, 204]:
            # Handle empty response (e.g., 204 No Content)
            if response.status_code == 204:
                return {"success": True}
            
            try:
                data = response.json()
                
                # Check if response indicates success
                if data.get('success'):
                    return data.get('data', {})
                else:
                    # Return error information
                    return {"error": data.get('message', 'Unknown error')}
            except ValueError:
                # Response is not JSON, return as success
                return {"success": True}
        else:
            # Error response
            try:
                error_data = response.json()
                return {"error": f"HTTP {response.status_code}: {error_data.get('message', response.text)}"}
            except ValueError:
                return {"error": f"HTTP {response.status_code}: {response.text}"}
    
    except Exception as e:
        return {"error": f"Request failed: {str(e)}"}
