"""
Cloudbeds MCP server.

This module creates and configures the FastMCP server for Cloudbeds API.
"""

from fastmcp import FastMCP
from typing import Dict, List, Optional, Any

# Import tools
from src.tools.reservations import (
    get_reservations,
    get_reservation_details,
    get_reservation_invoice,
    create_reservation,
    update_reservation,
    update_reservation_status,
    update_reservation_checkout_date,
    update_reservation_arrival_time,
)
from src.tools.rooms import (
    get_room_types,
    get_rooms,
    get_room_availability,
    get_room_rates,
)
from src.tools.guests import (
    get_guest_details,
    search_guests,
    create_guest,
    update_guest,
)

# Import resources
from src.resources.reservations import (
    get_recent_reservations,
    get_reservation_by_id,
)
from src.resources.rooms import (
    get_all_room_types,
    get_all_rooms,
    get_availability_next_30_days,
)
from src.resources.property import (
    get_property_info,
    get_property_settings,
)

# Import prompts
from src.prompts.reservation_prompts import (
    summarize_reservation_prompt,
    create_reservation_prompt,
)

# Create FastMCP instance
mcp = FastMCP(name="CloudbedsMCP")
# Define dependencies for fastmcp dev command
mcp.dependencies = [
    "httpx",
    "python-dotenv",
    "pydantic",
    "pydantic-settings",
    "uvicorn",
    "websockets"
]

# Register tools
@mcp.tool()
async def get_reservations_tool(days_back: int = 30) -> List[Dict[str, Any]]:
    """
    Retrieve reservations from Cloudbeds API.

    Args:
        days_back (int): Number of days to look back for reservations

    Returns:
        List[Dict[str, Any]]: List of reservation data
    """
    return await get_reservations(days_back=days_back)

@mcp.tool()
async def get_reservation_tool(reservation_id: str) -> Dict[str, Any]:
    """
    Retrieve detailed information for a specific reservation.

    Args:
        reservation_id (str): Reservation ID

    Returns:
        Dict[str, Any]: Detailed reservation data
    """
    return await get_reservation_details(reservation_id=reservation_id)

@mcp.tool()
async def get_invoice_tool(reservation_id: str) -> Dict[str, Any]:
    """
    Retrieve invoice information for a specific reservation.

    Args:
        reservation_id (str): Reservation ID

    Returns:
        Dict[str, Any]: Invoice data
    """
    return await get_reservation_invoice(reservation_id=reservation_id)

@mcp.tool()
async def create_reservation_tool(reservation_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Create a new reservation in Cloudbeds.

    Args:
        reservation_data (Dict[str, Any]): Reservation data

    Returns:
        Dict[str, Any]: Created reservation data
    """
    return await create_reservation(reservation_data=reservation_data)

@mcp.tool()
async def update_reservation_tool(reservation_id: str, update_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Update an existing reservation in Cloudbeds.

    Args:
        reservation_id (str): Reservation ID
        update_data (Dict[str, Any]): Data to update

    Returns:
        Dict[str, Any]: Updated reservation data
    """
    return await update_reservation(reservation_id=reservation_id, update_data=update_data)

@mcp.tool()
async def update_reservation_status_tool(reservation_id: str, status: str) -> Dict[str, Any]:
    """
    Update reservation status - a convenient tool for common status changes.

    Args:
        reservation_id (str): Reservation ID
        status (str): New status (confirmed, cancelled, checked_in, checked_out, no_show)

    Returns:
        Dict[str, Any]: Updated reservation data
    """
    return await update_reservation_status(reservation_id=reservation_id, status=status)

@mcp.tool()
async def update_reservation_checkout_date_tool(reservation_id: str, checkout_date: str) -> Dict[str, Any]:
    """
    Update reservation checkout date - a convenient tool for date changes.

    Args:
        reservation_id (str): Reservation ID
        checkout_date (str): New checkout date in YYYY-MM-DD format

    Returns:
        Dict[str, Any]: Updated reservation data
    """
    return await update_reservation_checkout_date(reservation_id=reservation_id, checkout_date=checkout_date)

@mcp.tool()
async def update_reservation_arrival_time_tool(reservation_id: str, arrival_time: str) -> Dict[str, Any]:
    """
    Update reservation estimated arrival time - a convenient tool for arrival time changes.

    Args:
        reservation_id (str): Reservation ID
        arrival_time (str): Estimated arrival time in HH:MM format (e.g., "15:30")

    Returns:
        Dict[str, Any]: Updated reservation data
    """
    return await update_reservation_arrival_time(reservation_id=reservation_id, arrival_time=arrival_time)

@mcp.tool()
async def get_room_types_tool() -> List[Dict[str, Any]]:
    """
    Retrieve room types from Cloudbeds API.

    Returns:
        List[Dict[str, Any]]: List of room type data
    """
    return await get_room_types()

@mcp.tool()
async def get_rooms_tool() -> List[Dict[str, Any]]:
    """
    Retrieve rooms from Cloudbeds API.

    Returns:
        List[Dict[str, Any]]: List of room data
    """
    return await get_rooms()

@mcp.tool()
async def get_availability_tool(start_date: str, end_date: str) -> List[Dict[str, Any]]:
    """
    Retrieve room availability for a date range.

    Args:
        start_date (str): Start date in YYYY-MM-DD format
        end_date (str): End date in YYYY-MM-DD format

    Returns:
        List[Dict[str, Any]]: List of availability data
    """
    return await get_room_availability(start_date=start_date, end_date=end_date)

@mcp.tool()
async def get_room_rates_tool(start_date: str, end_date: str) -> List[Dict[str, Any]]:
    """
    Retrieve room rates for a date range.

    Args:
        start_date (str): Start date in YYYY-MM-DD format
        end_date (str): End date in YYYY-MM-DD format

    Returns:
        List[Dict[str, Any]]: List of rate data
    """
    return await get_room_rates(start_date=start_date, end_date=end_date)

@mcp.tool()
async def search_guests_tool(search_term: str) -> List[Dict[str, Any]]:
    """
    Search for guests by name, email, or phone.

    Args:
        search_term (str): Search term

    Returns:
        List[Dict[str, Any]]: List of matching guest data
    """
    return await search_guests(search_term=search_term)

@mcp.tool()
async def get_guest_tool(guest_id: str) -> Dict[str, Any]:
    """
    Retrieve detailed information for a specific guest.

    Args:
        guest_id (str): Guest ID

    Returns:
        Dict[str, Any]: Detailed guest data
    """
    return await get_guest_details(guest_id=guest_id)

@mcp.tool()
async def create_guest_tool(guest_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Create a new guest in Cloudbeds.

    Args:
        guest_data (Dict[str, Any]): Guest data

    Returns:
        Dict[str, Any]: Created guest data
    """
    return await create_guest(guest_data=guest_data)

@mcp.tool()
async def update_guest_tool(guest_id: str, update_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Update an existing guest in Cloudbeds.

    Args:
        guest_id (str): Guest ID
        update_data (Dict[str, Any]): Data to update

    Returns:
        Dict[str, Any]: Updated guest data
    """
    return await update_guest(guest_id=guest_id, update_data=update_data)

# Register resources
@mcp.resource("cloudbeds://reservations/recent")
async def recent_reservations_resource() -> List[Dict[str, Any]]:
    """Get recent reservations from the last 30 days."""
    return await get_recent_reservations()

@mcp.resource("cloudbeds://reservations/{reservation_id}")
async def reservation_resource(reservation_id: str) -> Dict[str, Any]:
    """Get a specific reservation by ID."""
    return await get_reservation_by_id(reservation_id)

@mcp.resource("cloudbeds://rooms/types")
async def room_types_resource() -> List[Dict[str, Any]]:
    """Get all room types."""
    return await get_all_room_types()

@mcp.resource("cloudbeds://rooms")
async def rooms_resource() -> List[Dict[str, Any]]:
    """Get all rooms."""
    return await get_all_rooms()

@mcp.resource("cloudbeds://availability/next30days")
async def availability_resource() -> List[Dict[str, Any]]:
    """Get availability for the next 30 days."""
    return await get_availability_next_30_days()

@mcp.resource("cloudbeds://property")
async def property_resource() -> Dict[str, Any]:
    """Get property information."""
    return await get_property_info()

@mcp.resource("cloudbeds://guests/search/{search_term}")
async def guest_search_resource(search_term: str) -> List[Dict[str, Any]]:
    """Search for guests by name, email, or phone."""
    return await search_guests(search_term)

@mcp.resource("cloudbeds://guests/{guest_id}")
async def guest_resource(guest_id: str) -> Dict[str, Any]:
    """Get a specific guest by ID."""
    return await get_guest_details(guest_id)

# Register prompts
@mcp.prompt()
def summarize_reservation(reservation_data: Dict[str, Any]) -> str:
    """Generate a prompt to summarize reservation data."""
    return summarize_reservation_prompt(reservation_data)

@mcp.prompt()
def new_reservation_prompt() -> str:
    """Generate a prompt for creating a new reservation."""
    return create_reservation_prompt()
