"""
Reservation tools for Cloudbeds MCP.

This module provides tools for interacting with Cloudbeds reservations.
"""

import datetime
from typing import Dict, List, Optional, Any

from src.cloudbeds_client import api_request
from src.config import CLOUDBEDS_PROPERTY_ID

async def get_reservations(
    days_back: int = 30,
    property_id: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Retrieve reservations from Cloudbeds API.

    Args:
        days_back (int): Number of days to look back for reservations
        property_id (str, optional): Cloudbeds property ID

    Returns:
        list: List of reservation data
    """
    # Calculate date range
    end_date = datetime.datetime.now() + datetime.timedelta(days=400)  # Include future reservations
    start_date = datetime.datetime.now() - datetime.timedelta(days=days_back)

    # Format dates for API
    start_date_str = start_date.strftime('%Y-%m-%d')
    end_date_str = end_date.strftime('%Y-%m-%d')

    # Request parameters
    params = {
        "checkInFrom": start_date_str,
        "checkInTo": end_date_str,
        "includeGuestInfo": "true",
        "includeRoomInfo": "true"
    }

    return await api_request("getReservations", params, property_id or CLOUDBEDS_PROPERTY_ID)

async def get_reservation_details(
    reservation_id: str,
    property_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Retrieve detailed information for a specific reservation.

    Args:
        reservation_id (str): Reservation ID
        property_id (str, optional): Cloudbeds property ID

    Returns:
        dict: Detailed reservation data
    """
    # Request parameters
    params = {
        "reservationID": reservation_id
    }

    return await api_request("getReservation", params, property_id or CLOUDBEDS_PROPERTY_ID)

async def get_reservation_invoice(
    reservation_id: str,
    property_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Retrieve invoice information for a specific reservation.

    Args:
        reservation_id (str): Reservation ID
        property_id (str, optional): Cloudbeds property ID

    Returns:
        dict: Invoice data
    """
    # Request parameters
    params = {
        "reservationID": reservation_id
    }

    return await api_request("getInvoice", params, property_id or CLOUDBEDS_PROPERTY_ID)

async def create_reservation(
    reservation_data: Dict[str, Any],
    property_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Create a new reservation in Cloudbeds.

    Args:
        reservation_data (dict): Reservation data containing:
            - startDate (str): Check-in date in YYYY-MM-DD format
            - endDate (str): Check-out date in YYYY-MM-DD format
            - guestFirstName (str): Guest first name
            - guestLastName (str): Guest last name
            - guestEmail (str): Guest email
            - adults (int): Number of adults
            - roomTypeID (str): Room type ID
            - guestCountry (str): Guest country code (e.g., "US", "FR")
            - children (int, optional): Number of children (default: 0)
            - paymentMethod (str, optional): Payment method (default: "cash")
            - rooms (str, optional): Number of rooms (default: "1")
            - source (str, optional): Reservation source
            - notes (str, optional): Special requests or notes
        property_id (str, optional): Cloudbeds property ID

    Returns:
        dict: Created reservation data or error information
        
    Note:
        This function requires form data (not JSON) for the Cloudbeds API.
        All parameters are validated and the API expects specific formats.
        Country codes may be property-specific - check with property settings.
    """
    # Validate required fields (updated based on API requirements)
    required_fields = ['startDate', 'endDate', 'guestFirstName', 'guestLastName', 'guestEmail', 'adults', 'roomTypeID', 'guestCountry']
    missing_fields = [field for field in required_fields if field not in reservation_data]

    if missing_fields:
        return {
            "error": f"Missing required fields: {', '.join(missing_fields)}",
            "required_fields": required_fields
        }

    # Validate date format
    try:
        datetime.datetime.strptime(reservation_data['startDate'], '%Y-%m-%d')
        datetime.datetime.strptime(reservation_data['endDate'], '%Y-%m-%d')
    except ValueError:
        return {"error": "Invalid date format. Use YYYY-MM-DD format for startDate and endDate"}

    # Validate check-in is before check-out
    start_date = datetime.datetime.strptime(reservation_data['startDate'], '%Y-%m-%d')
    end_date = datetime.datetime.strptime(reservation_data['endDate'], '%Y-%m-%d')

    if start_date >= end_date:
        return {"error": "Check-in date must be before check-out date"}

    # Use form data with all required parameters discovered from API testing
    mapped_data = {
        'startDate': reservation_data['startDate'],
        'endDate': reservation_data['endDate'],
        'guestFirstName': reservation_data['guestFirstName'],
        'guestLastName': reservation_data['guestLastName'],
        'guestEmail': reservation_data['guestEmail'],
        'adults': str(reservation_data['adults']),  # Convert to string
        'roomTypeID': reservation_data['roomTypeID'],
        'rooms': str(reservation_data.get('rooms', 1)),  # Required parameter, default to 1
        'children': str(reservation_data.get('children', 0)),  # Default to 0, convert to string
        'paymentMethod': reservation_data.get('paymentMethod', 'cash'),  # Default to cash
        'guestCountry': reservation_data['guestCountry']
    }
    
    # Add optional fields if provided
    optional_fields = ['guestPhone', 'source', 'notes', 'ratePlanID']
    for field in optional_fields:
        if field in reservation_data:
            mapped_data[field] = reservation_data[field]

    # Add debug logging to see what's being sent
    print(f"DEBUG: Sending reservation data: {mapped_data}")

    return await api_request("postReservation", data=mapped_data, property_id=property_id or CLOUDBEDS_PROPERTY_ID, method="POST")

async def update_reservation(
    reservation_id: str,
    update_data: Dict[str, Any],
    property_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Update an existing reservation in Cloudbeds.

    Args:
        reservation_id (str): Reservation ID
        update_data (dict): Data to update, can include:
            - checkoutDate (str): Check-out date in YYYY-MM-DD format
            - status (str): Reservation status (confirmed, cancelled, etc.)
            - estimatedArrivalTime (str): Estimated arrival time in HH:MM format
            - customFields (dict): Custom field data
            - rooms (list): Room assignment data
            - dateCreated (str): Date created in YYYY-MM-DD format
            - guestFirstName (str): Guest first name
            - guestLastName (str): Guest last name
            - guestEmail (str): Guest email
            - adults (int): Number of adults
            - children (int): Number of children
            - notes (str): Special requests or notes
        property_id (str, optional): Cloudbeds property ID

    Returns:
        dict: Updated reservation data or error information
    """
    if not reservation_id:
        return {"error": "Reservation ID is required"}

    if not update_data:
        return {"error": "Update data is required"}

    # Map common field names to Cloudbeds API expected names
    field_mapping = {
        'endDate': 'checkoutDate',
        'startDate': 'checkInDate',  # Note: startDate updates may not be allowed
    }

    # Apply field mapping
    mapped_data = {}
    for key, value in update_data.items():
        mapped_key = field_mapping.get(key, key)
        mapped_data[mapped_key] = value

    # Validate date formats if provided
    if 'checkoutDate' in mapped_data:
        try:
            datetime.datetime.strptime(mapped_data['checkoutDate'], '%Y-%m-%d')
        except ValueError:
            return {"error": "Invalid checkoutDate format. Use YYYY-MM-DD format"}

    if 'checkInDate' in mapped_data:
        try:
            datetime.datetime.strptime(mapped_data['checkInDate'], '%Y-%m-%d')
        except ValueError:
            return {"error": "Invalid checkInDate format. Use YYYY-MM-DD format"}

    # Validate estimated arrival time format if provided
    if 'estimatedArrivalTime' in mapped_data:
        try:
            datetime.datetime.strptime(mapped_data['estimatedArrivalTime'], '%H:%M')
        except ValueError:
            return {"error": "Invalid estimatedArrivalTime format. Use HH:MM format (e.g., '14:30')"}

    # Check that at least one required parameter is provided
    required_params = ['customFields', 'estimatedArrivalTime', 'rooms', 'status', 'checkoutDate', 'dateCreated']
    has_required = any(param in mapped_data for param in required_params)

    if not has_required:
        return {
            "error": f"At least one of the following parameters is required: {', '.join(required_params)}",
            "available_fields": required_params,
            "examples": {
                "status": "confirmed, cancelled, checked_in, checked_out, no_show",
                "checkoutDate": "2024-12-31 (YYYY-MM-DD format)",
                "estimatedArrivalTime": "14:30 (HH:MM format)",
                "customFields": {"field_name": "field_value"},
                "rooms": [{"roomID": "room_id", "guestID": "guest_id"}],
                "dateCreated": "2024-01-01 (YYYY-MM-DD format)"
            }
        }

    # Add reservation ID to the update data
    mapped_data["reservationID"] = reservation_id

    return await api_request("putReservation", data=mapped_data, property_id=property_id or CLOUDBEDS_PROPERTY_ID, method="PUT")


# Helper functions for common reservation update scenarios

async def update_reservation_status(
    reservation_id: str,
    status: str,
    property_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Update reservation status - a common operation.

    Args:
        reservation_id (str): Reservation ID
        status (str): New status (confirmed, cancelled, checked_in, checked_out, no_show)
        property_id (str, optional): Cloudbeds property ID

    Returns:
        dict: Updated reservation data or error information
    """
    return await update_reservation(reservation_id, {"status": status}, property_id)


async def update_reservation_checkout_date(
    reservation_id: str,
    checkout_date: str,
    property_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Update reservation checkout date - a common operation.

    Args:
        reservation_id (str): Reservation ID
        checkout_date (str): New checkout date in YYYY-MM-DD format
        property_id (str, optional): Cloudbeds property ID

    Returns:
        dict: Updated reservation data or error information
    """
    return await update_reservation(reservation_id, {"endDate": checkout_date}, property_id)


async def update_reservation_arrival_time(
    reservation_id: str,
    arrival_time: str,
    property_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Update reservation estimated arrival time - a common operation.

    Args:
        reservation_id (str): Reservation ID
        arrival_time (str): Estimated arrival time in HH:MM format (e.g., "15:30")
        property_id (str, optional): Cloudbeds property ID

    Returns:
        dict: Updated reservation data or error information
    """
    return await update_reservation(reservation_id, {"estimatedArrivalTime": arrival_time}, property_id)
