#!/usr/bin/env python3
"""
Debug reservation creation to identify the exact parameter issue.
"""

import asyncio
import sys
sys.path.append('.')

from src.tools.reservations import create_reservation

async def test_different_parameter_combinations():
    """Test different parameter combinations to debug the 'Invalid Parameter Format' error."""
    
    print("🐛 Debugging Reservation Creation")
    print("=" * 50)
    
    # Base valid data set based on what we know works from existing reservations
    base_data = {
        "startDate": "2025-06-01",
        "endDate": "2025-06-03",
        "guestFirstName": "Test",
        "guestLastName": "User", 
        "guestEmail": "<EMAIL>",
        "adults": 2,
        "roomTypeID": "653498",  # <PERSON> Junior - known to exist
        "guestCountry": "US"
    }
    
    # Test 1: Base data - should work according to our understanding
    print("\n1. Testing with base data...")
    result = await create_reservation(base_data.copy())
    print(f"   Result: {result}")
    
    # Test 2: Try with guest<PERSON>hone included
    print("\n2. Testing with guestPhone...")
    data_with_phone = base_data.copy()
    data_with_phone["guestPhone"] = "+1234567890"
    result = await create_reservation(data_with_phone)
    print(f"   Result: {result}")
    
    # Test 3: Try with specific rate plan ID (if exists)
    print("\n3. Testing with explicit rooms=1...")
    data_with_rooms = base_data.copy()
    data_with_rooms["rooms"] = 1
    result = await create_reservation(data_with_rooms)
    print(f"   Result: {result}")
    
    # Test 4: Try with source
    print("\n4. Testing with source...")
    data_with_source = base_data.copy()
    data_with_source["source"] = "s-1"  # Website/Booking Engine source ID from existing reservation
    result = await create_reservation(data_with_source)
    print(f"   Result: {result}")
    
    # Test 5: Try with different guestCountry format
    print("\n5. Testing with different guestCountry (FR)...")
    data_with_fr = base_data.copy()
    data_with_fr["guestCountry"] = "FR"  # Same as existing reservation
    result = await create_reservation(data_with_fr)
    print(f"   Result: {result}")
    
    # Test 6: Try a minimal set (exactly what's required)
    print("\n6. Testing with minimal required fields only...")
    minimal_data = {
        "startDate": "2025-06-01",
        "endDate": "2025-06-03", 
        "guestFirstName": "Test",
        "guestLastName": "User",
        "guestEmail": "<EMAIL>",
        "adults": 2,
        "roomTypeID": "653498",
        "guestCountry": "US"
    }
    result = await create_reservation(minimal_data)
    print(f"   Result: {result}")
    
    # Test 7: Try with everything that's optional
    print("\n7. Testing with all optional fields...")
    full_data = {
        "startDate": "2025-06-01",
        "endDate": "2025-06-03",
        "guestFirstName": "Test", 
        "guestLastName": "User",
        "guestEmail": "<EMAIL>",
        "adults": 2,
        "children": 0,
        "roomTypeID": "653498",
        "guestCountry": "US",
        "guestPhone": "+1234567890",
        "paymentMethod": "cash",
        "rooms": 1,
        "source": "s-1",
        "notes": "Test reservation"
    }
    result = await create_reservation(full_data)
    print(f"   Result: {result}")

if __name__ == "__main__":
    asyncio.run(test_different_parameter_combinations()) 