#!/usr/bin/env python3
"""
Test script to understand the exact parameter requirements for Cloudbeds API operations.
"""

import asyncio
import sys
import os
sys.path.append('.')

from src.tools.reservations import create_reservation, update_reservation
from src.tools.guests import create_guest, update_guest

async def test_parameter_requirements():
    """Test different parameter combinations to understand API requirements."""
    
    print("🧪 Testing Parameter Requirements")
    print("=" * 50)
    
    # Test 1: Update reservation with status (one of the required fields)
    print("\n1. Testing update_reservation with status...")
    try:
        result = await update_reservation("test-reservation-id", {"status": "confirmed"})
        print(f"   Result: {result}")
        
        if "error" in result:
            if "At least one of the following parameter(s)" in result["error"]:
                print("   ❌ Still getting parameter requirement error")
            elif "property ID" in result["error"]:
                print("   ❌ Property ID error returned")
            else:
                print(f"   ⚠️  Different error (expected): {result['error']}")
        else:
            print("   ✅ Parameter validation passed!")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # Test 2: Update reservation with checkoutDate
    print("\n2. Testing update_reservation with checkoutDate...")
    try:
        result = await update_reservation("test-reservation-id", {"endDate": "2024-06-10"})
        print(f"   Result: {result}")
        
        if "error" in result:
            if "At least one of the following parameter(s)" in result["error"]:
                print("   ❌ Still getting parameter requirement error")
            else:
                print(f"   ⚠️  Different error (expected): {result['error']}")
        else:
            print("   ✅ Parameter validation passed!")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # Test 3: Update reservation with estimatedArrivalTime
    print("\n3. Testing update_reservation with estimatedArrivalTime...")
    try:
        result = await update_reservation("test-reservation-id", {"estimatedArrivalTime": "15:30"})
        print(f"   Result: {result}")
        
        if "error" in result:
            if "At least one of the following parameter(s)" in result["error"]:
                print("   ❌ Still getting parameter requirement error")
            else:
                print(f"   ⚠️  Different error (expected): {result['error']}")
        else:
            print("   ✅ Parameter validation passed!")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # Test 4: Create reservation (should work with current parameters)
    print("\n4. Testing create_reservation...")
    reservation_data = {
        "startDate": "2024-06-01",
        "endDate": "2024-06-05",
        "guestFirstName": "Test",
        "guestLastName": "User",
        "guestEmail": "<EMAIL>",
        "adults": 2,
        "roomTypeID": "test-room-type"
    }
    
    try:
        result = await create_reservation(reservation_data)
        print(f"   Result: {result}")
        
        if "error" in result:
            if "property ID" in result["error"]:
                print("   ❌ Property ID error returned")
            elif "parameter" in result["error"].lower():
                print(f"   ⚠️  Parameter error: {result['error']}")
            else:
                print(f"   ⚠️  Different error (may be expected): {result['error']}")
        else:
            print("   ✅ Create reservation successful!")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # Test 5: Create guest (should work)
    print("\n5. Testing create_guest...")
    guest_data = {
        "guestFirstName": "Test",
        "guestLastName": "Guest",
        "guestEmail": "<EMAIL>"
    }
    
    try:
        result = await create_guest(guest_data)
        print(f"   Result: {result}")
        
        if "error" in result:
            if "property ID" in result["error"]:
                print("   ❌ Property ID error returned")
            else:
                print(f"   ⚠️  Different error (may be expected): {result['error']}")
        else:
            print("   ✅ Create guest successful!")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    print("\n" + "=" * 50)
    print("🏁 Parameter testing completed!")

async def main():
    """Main test function."""
    
    if not os.getenv("CLOUDBEDS_API_KEY"):
        print("❌ CLOUDBEDS_API_KEY environment variable not set")
        return
    
    if not os.getenv("CLOUDBEDS_PROPERTY_ID"):
        print("❌ CLOUDBEDS_PROPERTY_ID environment variable not set")
        return
    
    await test_parameter_requirements()

if __name__ == "__main__":
    asyncio.run(main())
